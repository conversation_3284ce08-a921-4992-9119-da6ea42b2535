defmodule Drops.Relation do
  @moduledoc """
  Provides a convenient query API that wraps Ecto.Schema and delegates to Ecto.Repo functions.

  This module generates relation modules that automatically infer database schemas and provide
  a query API that delegates to the configured Ecto repository. All functions accept an optional
  `:repo` option that overrides the default repository configured via the `use` macro.

  ## Usage

      defmodule MyApp.Users do
        use Drops.Relation, repo: MyApp.Repo, name: "users"
      end

      # Query functions automatically use the configured repo
      user = MyApp.Users.get(1)
      users = MyApp.Users.all()

  ## Query API

  All query functions delegate to the corresponding `Ecto.Repo` functions. See the
  [Ecto.Repo documentation](https://hexdocs.pm/ecto/Ecto.Repo.html) for detailed information
  about each function's behavior and options.

  The `:repo` option is automatically passed based on the repository configured in the `use` macro,
  but can be overridden by passing a `:repo` option to any function call.
  """

  @callback struct() :: module()

  defmacro __using__(opts) do
    quote do
      @behaviour Drops.Relation

      import Drops.Relation

      Module.register_attribute(__MODULE__, :fields, accumulate: true)

      @before_compile Drops.Relation
      @after_compile Drops.Relation

      @opts unquote(opts)

      defstruct([:struct, :repo, queryable: [], opts: [], preloads: []])
    end
  end

  defmacro associations(do: block) do
    quote do
      @associations unquote(Macro.escape(block))
    end
  end

  defmacro field(name, type, opts \\ []) do
    quote do
      @fields unquote(Macro.escape({name, type, opts}))
    end
  end

  # Query API functions - these are defined at module level for proper documentation
  # and delegate to Ecto.Repo functions with the configured repository

  @doc """
  Gets a single record by primary key.

  Delegates to `Ecto.Repo.get/3`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.get(1)
      user = MyRelation.get(1, repo: AnotherRepo)

  See [Ecto.Repo.get/3](https://hexdocs.pm/ecto/Ecto.Repo.html#c:get/3) for more details.
  """
  def get(queryable, id, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.get(actual_queryable, id, cleaned_opts)
  end

  @doc """
  Gets a single record by primary key, raises if not found.

  Delegates to `Ecto.Repo.get!/3`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.get!(1)
      user = MyRelation.get!(1, repo: AnotherRepo)

  See [Ecto.Repo.get!/3](https://hexdocs.pm/ecto/Ecto.Repo.html#c:get!/3) for more details.
  """
  def get!(queryable, id, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.get!(actual_queryable, id, cleaned_opts)
  end

  @doc """
  Gets a single record by the given clauses.

  Delegates to `Ecto.Repo.get_by/3`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.get_by(email: "<EMAIL>")
      user = MyRelation.get_by([email: "<EMAIL>"], repo: AnotherRepo)

  See [Ecto.Repo.get_by/3](https://hexdocs.pm/ecto/Ecto.Repo.html#c:get_by/3) for more details.
  """
  def get_by(queryable, clauses, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.get_by(actual_queryable, clauses, cleaned_opts)
  end

  @doc """
  Gets a single record by the given clauses, raises if not found.

  Delegates to `Ecto.Repo.get_by!/3`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.get_by!(email: "<EMAIL>")
      user = MyRelation.get_by!([email: "<EMAIL>"], repo: AnotherRepo)

  See [Ecto.Repo.get_by!/3](https://hexdocs.pm/ecto/Ecto.Repo.html#c:get_by!/3) for more details.
  """
  def get_by!(queryable, clauses, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.get_by!(actual_queryable, clauses, cleaned_opts)
  end

  @doc """
  Fetches all records matching the given query.

  Delegates to `Ecto.Repo.all/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      users = MyRelation.all()
      users = MyRelation.all(repo: AnotherRepo)

  See [Ecto.Repo.all/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:all/2) for more details.
  """
  def all(queryable, opts \\ []) do
    opts[:repo].all(queryable, Keyword.delete(opts, :repo))
  end

  @doc """
  Fetches a single result from the query.

  Delegates to `Ecto.Repo.one/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.one(query)
      user = MyRelation.one(query, repo: AnotherRepo)

  See [Ecto.Repo.one/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:one/2) for more details.
  """
  def one(queryable, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.one(actual_queryable, cleaned_opts)
  end

  @doc """
  Fetches a single result from the query, raises if not found or more than one.

  Delegates to `Ecto.Repo.one!/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.one!(query)
      user = MyRelation.one!(query, repo: AnotherRepo)

  See [Ecto.Repo.one!/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:one!/2) for more details.
  """
  def one!(queryable, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.one!(actual_queryable, cleaned_opts)
  end

  @doc """
  Inserts a struct, changeset, or plain map.

  Delegates to `Ecto.Repo.insert/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      {:ok, user} = MyRelation.insert(%{name: "John", email: "<EMAIL>"})
      {:ok, user} = MyRelation.insert(changeset, repo: AnotherRepo)

  See [Ecto.Repo.insert/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:insert/2) for more details.
  """
  def insert(struct_or_changeset, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_struct =
      case struct_or_changeset do
        %{__struct__: _} ->
          # Already a struct or changeset
          struct_or_changeset

        %{} = plain_map when relation_module != nil ->
          # Plain map - convert to struct first using relation module
          struct_module = Module.concat(relation_module, Struct)
          struct(struct_module, plain_map)

        %{} = plain_map ->
          # Plain map without relation module - pass as is
          plain_map
      end

    repo.insert(actual_struct, cleaned_opts)
  end

  @doc """
  Inserts a struct, changeset, or plain map, raises on error.

  Delegates to `Ecto.Repo.insert!/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.insert!(%{name: "John", email: "<EMAIL>"})
      user = MyRelation.insert!(changeset, repo: AnotherRepo)

  See [Ecto.Repo.insert!/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:insert!/2) for more details.
  """
  def insert!(struct_or_changeset, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_struct =
      case struct_or_changeset do
        %{__struct__: _} ->
          # Already a struct or changeset
          struct_or_changeset

        %{} = plain_map when relation_module != nil ->
          # Plain map - convert to struct first using relation module
          struct_module = Module.concat(relation_module, Struct)
          struct(struct_module, plain_map)

        %{} = plain_map ->
          # Plain map without relation module - pass as is
          plain_map
      end

    repo.insert!(actual_struct, cleaned_opts)
  end

  @doc """
  Updates a changeset.

  Delegates to `Ecto.Repo.update/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      {:ok, user} = MyRelation.update(changeset)
      {:ok, user} = MyRelation.update(changeset, repo: AnotherRepo)

  See [Ecto.Repo.update/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:update/2) for more details.
  """
  def update(changeset, opts \\ []) do
    repo = opts[:repo]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)
    repo.update(changeset, cleaned_opts)
  end

  @doc """
  Updates a changeset, raises on error.

  Delegates to `Ecto.Repo.update!/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.update!(changeset)
      user = MyRelation.update!(changeset, repo: AnotherRepo)

  See [Ecto.Repo.update!/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:update!/2) for more details.
  """
  def update!(changeset, opts \\ []) do
    repo = opts[:repo]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)
    repo.update!(changeset, cleaned_opts)
  end

  @doc """
  Deletes a struct.

  Delegates to `Ecto.Repo.delete/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      {:ok, user} = MyRelation.delete(user)
      {:ok, user} = MyRelation.delete(user, repo: AnotherRepo)

  See [Ecto.Repo.delete/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:delete/2) for more details.
  """
  def delete(struct, opts \\ []) do
    repo = opts[:repo]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)
    repo.delete(struct, cleaned_opts)
  end

  @doc """
  Deletes a struct, raises on error.

  Delegates to `Ecto.Repo.delete!/2`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.delete!(user)
      user = MyRelation.delete!(user, repo: AnotherRepo)

  See [Ecto.Repo.delete!/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:delete!/2) for more details.
  """
  def delete!(struct, opts \\ []) do
    repo = opts[:repo]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)
    repo.delete!(struct, cleaned_opts)
  end

  @doc """
  Returns the count of records.

  Delegates to `Ecto.Repo.aggregate/3`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      count = MyRelation.count()
      count = MyRelation.count(repo: AnotherRepo)

  See [Ecto.Repo.aggregate/3](https://hexdocs.pm/ecto/Ecto.Repo.html#c:aggregate/3) for more details.
  """
  def count(queryable, aggregate \\ :count, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.aggregate(actual_queryable, aggregate, cleaned_opts)
  end

  @doc """
  Returns the first record.

  Delegates to `Ecto.Repo.one/2` with `Ecto.Query.first/1`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.first()
      user = MyRelation.first(repo: AnotherRepo)

  See [Ecto.Repo.one/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:one/2) and
  [Ecto.Query.first/1](https://hexdocs.pm/ecto/Ecto.Query.html#first/1) for more details.
  """
  def first(queryable, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.one(Ecto.Query.first(actual_queryable), cleaned_opts)
  end

  @doc """
  Returns the last record.

  Delegates to `Ecto.Repo.one/2` with `Ecto.Query.last/1`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.last()
      user = MyRelation.last(repo: AnotherRepo)

  See [Ecto.Repo.one/2](https://hexdocs.pm/ecto/Ecto.Repo.html#c:one/2) and
  [Ecto.Query.last/1](https://hexdocs.pm/ecto/Ecto.Query.html#last/1) for more details.
  """
  def last(queryable, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        queryable
      end

    repo.one(Ecto.Query.last(actual_queryable), cleaned_opts)
  end

  @doc """
  Gets a record by a specific field value.

  This is a generic function used by dynamically generated index-based finders.
  Delegates to `Ecto.Repo.get_by/3`. The `:repo` and `:relation` options are automatically set
  based on the repository and relation module configured in the `use` macro, but can be overridden.

  ## Examples

      user = MyRelation.get_by_field(:email, "<EMAIL>")
      user = MyRelation.get_by_field(:email, "<EMAIL>", repo: AnotherRepo)

  See [Ecto.Repo.get_by/3](https://hexdocs.pm/ecto/Ecto.Repo.html#c:get_by/3) for more details.
  """
  def get_by_field(field, value, opts \\ []) do
    repo = opts[:repo]
    relation_module = opts[:relation]
    cleaned_opts = opts |> Keyword.delete(:repo) |> Keyword.delete(:relation)

    actual_queryable =
      if relation_module do
        Module.concat(relation_module, Struct)
      else
        # This shouldn't happen in normal usage since get_by_field is typically used with relations
        raise ArgumentError,
              "get_by_field requires :relation option when not called from a relation module"
      end

    repo.get_by(actual_queryable, [{field, value}], cleaned_opts)
  end

  defmacro __before_compile__(env) do
    relation = env.module

    opts = Module.get_attribute(relation, :opts)
    repo = opts[:repo]
    name = opts[:name]

    # Always get associations from the current module, regardless of caching
    association_definitions = Module.get_attribute(relation, :associations, [])

    # Get or infer the base schema (fields only, without associations)
    {base_ecto_schema_ast, drops_schema} =
      Drops.Relation.SchemaCache.get_or_infer_schema(repo, name, fn ->
        Drops.Relation.Inference.infer_schema_fields_only(relation, name, repo)
      end)

    # Always combine the base schema with current associations
    ecto_schema_ast =
      combine_schema_with_associations(
        base_ecto_schema_ast,
        association_definitions,
        name
      )

    # Generate the nested Schema module
    schema_module_ast = generate_schema_module(relation, ecto_schema_ast)

    # Generate query API functions
    query_api_ast = generate_query_api(opts, drops_schema)

    quote location: :keep do
      require unquote(repo)

      # Define the nested Schema module
      unquote(schema_module_ast)

      # Store configuration as module attributes
      @opts unquote(Macro.escape(opts))
      @schema unquote(Macro.escape(drops_schema))

      def new(queryable, struct, opts) do
        Kernel.struct(__MODULE__, %{
          queryable: queryable,
          struct: struct,
          repo: unquote(repo),
          opts: opts,
          preloads: []
        })
      end

      # Make the relation module itself queryable by implementing the necessary functions
      # This allows the relation module to be used directly in Ecto queries
      def __schema__(query) do
        Module.concat(__MODULE__, Struct).__schema__(query)
      end

      def __schema__(query, field) do
        Module.concat(__MODULE__, Struct).__schema__(query, field)
      end

      # Generate query API functions
      unquote_splicing(query_api_ast)

      @spec schema() :: Drops.Relation.Schema.t()
      def schema do
        @schema
      end

      # Handle when called with just options (e.g., users.restrict(name: "Jane"))
      def restrict(opts) when is_list(opts),
        do: __MODULE__.new(__MODULE__.Struct, __MODULE__.Struct, opts)

      def restrict(%__MODULE__{} = relation, opts) do
        # Preserve existing preloads when restricting a relation
        %{relation | opts: Keyword.merge(relation.opts, opts)}
      end

      # Handle composition between different relation types
      def restrict(%other_module{} = other_relation, opts)
          when other_module != __MODULE__ do
        # Check if this is a different relation module
        if is_relation_module?(other_module) do
          # Try to infer association between the relations
          case Drops.Relation.Composite.infer_association(other_module, __MODULE__) do
            nil ->
              # No association found, just create a regular restricted relation
              __MODULE__.new(__MODULE__.Struct, __MODULE__.Struct, opts)

            association ->
              # Create a composite relation with automatic preloading
              right_relation = __MODULE__.new(__MODULE__.Struct, __MODULE__.Struct, opts)

              Drops.Relation.Composite.new(
                other_relation,
                right_relation,
                association,
                unquote(repo)
              )
          end
        else
          # Not a relation module, treat as regular queryable
          __MODULE__.new(other_relation, __MODULE__.Struct, opts)
        end
      end

      def restrict(queryable, opts),
        do: __MODULE__.new(queryable, __MODULE__.Struct, opts)

      # Helper function to check if a module is a relation module
      defp is_relation_module?(module) do
        try do
          function_exported?(module, :restrict, 2) and
            function_exported?(module, :ecto_schema, 1) and
            function_exported?(module, :associations, 0)
        rescue
          _ -> false
        end
      end

      def ecto_schema(group), do: __MODULE__.Struct.__schema__(group)
      def ecto_schema(group, name), do: __MODULE__.Struct.__schema__(group, name)

      def association(name), do: __MODULE__.Struct.__schema__(:association, name)
      def associations(), do: __MODULE__.Struct.__schema__(:associations)

      def struct(attributes \\ %{}) do
        struct(__MODULE__.Struct, attributes)
      end

      @doc """
      Preloads associations for the relation.

      This function creates a new relation with the specified associations marked for preloading.
      When the relation is enumerated or converted to a query, the associations will be preloaded.

      ## Parameters

      - `associations` - An atom, list of atoms, or keyword list specifying which associations to preload

      ## Examples

          # Preload a single association
          users.preload(:posts)

          # Preload multiple associations
          users.preload([:posts, :comments])

          # Preload with nested associations
          users.preload(posts: [:comments])

      ## Returns

      A new relation struct with the preload configuration applied.
      """
      def preload(associations) when is_atom(associations) or is_list(associations) do
        preload(__MODULE__.new(__MODULE__.Struct, __MODULE__.Struct, []), associations)
      end

      def preload(%__MODULE__{} = relation, associations)
          when is_atom(associations) or is_list(associations) do
        # Normalize associations to a list
        normalized_associations =
          case associations do
            atom when is_atom(atom) -> [atom]
            list when is_list(list) -> list
          end

        # Merge with existing preloads
        updated_preloads = relation.preloads ++ normalized_associations

        %{relation | preloads: updated_preloads}
      end
    end
  end

  defmacro __after_compile__(env, _) do
    module = env.module

    quote location: :keep do
      defimpl Enumerable, for: unquote(module) do
        import Ecto.Query

        def count(relation) do
          {:ok, length(materialize(relation))}
        end

        def member?(relation, value) do
          case materialize(relation) do
            {:ok, list} -> {:ok, value in list}
            {:error, _} = error -> error
          end
        end

        def slice(relation) do
          list = materialize(relation)
          size = length(list)

          {:ok, size, fn start, count, _step -> Enum.slice(list, start, count) end}
        end

        def reduce(relation, acc, fun) do
          Enumerable.List.reduce(materialize(relation), acc, fun)
        end

        defp materialize(relation) do
          unquote(module).all(relation)
        end
      end

      defimpl Ecto.Queryable, for: unquote(env.module) do
        import Ecto.Query

        def to_query(relation) do
          base_query = Ecto.Queryable.to_query(relation.struct)

          # Apply restrictions from opts
          query_with_restrictions =
            build_query_with_restrictions(base_query, relation.opts)

          # Apply preloads if any
          apply_preloads(query_with_restrictions, relation.preloads)
        end

        # Builds an Ecto query with WHERE clauses based on the restriction options
        defp build_query_with_restrictions(queryable, []) do
          queryable
        end

        defp build_query_with_restrictions(queryable, opts) do
          Enum.reduce(opts, queryable, fn {field, value}, query ->
            where(query, [r], field(r, ^field) == ^value)
          end)
        end

        # Applies preloads to the query
        defp apply_preloads(queryable, []) do
          queryable
        end

        defp apply_preloads(queryable, preloads) do
          from(q in queryable, preload: ^preloads)
        end
      end
    end
  end

  # Generates the nested Struct module AST
  defp generate_schema_module(relation, ecto_schema_ast) do
    struct_module_name = Module.concat(relation, Struct)

    quote location: :keep do
      defmodule unquote(struct_module_name) do
        use Ecto.Schema

        unquote(ecto_schema_ast)
      end
    end
  end

  # Generates query API functions that delegate to module-level functions
  defp generate_query_api(opts, drops_schema) do
    repo = opts[:repo]

    # Basic Ecto.Repo functions that delegate to module-level functions
    basic_functions = [
      generate_delegating_get_function(repo),
      generate_delegating_get_bang_function(repo),
      generate_delegating_get_by_function(repo),
      generate_delegating_get_by_bang_function(repo),
      generate_delegating_all_function(repo),
      generate_delegating_one_function(repo),
      generate_delegating_one_bang_function(repo),
      generate_delegating_insert_function(repo),
      generate_delegating_insert_bang_function(repo),
      generate_delegating_update_function(repo),
      generate_delegating_update_bang_function(repo),
      generate_delegating_delete_function(repo),
      generate_delegating_delete_bang_function(repo),
      generate_delegating_count_function(repo),
      generate_delegating_first_function(repo),
      generate_delegating_last_function(repo)
    ]

    # Index-based finder functions
    index_functions = generate_index_based_finders(repo, drops_schema)

    basic_functions ++ index_functions
  end

  # Delegating function generators - these generate functions that delegate to module-level functions
  defp generate_delegating_get_function(repo) do
    quote do
      def get(id, opts \\ []) do
        Drops.Relation.get(
          Module.concat(__MODULE__, Struct),
          id,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_get_bang_function(repo) do
    quote do
      def get!(id, opts \\ []) do
        Drops.Relation.get!(
          Module.concat(__MODULE__, Struct),
          id,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_get_by_function(repo) do
    quote do
      def get_by(clauses, opts \\ []) do
        Drops.Relation.get_by(
          Module.concat(__MODULE__, Struct),
          clauses,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_get_by_bang_function(repo) do
    quote do
      def get_by!(clauses, opts \\ []) do
        Drops.Relation.get_by!(
          Module.concat(__MODULE__, Struct),
          clauses,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_all_function(repo) do
    quote do
      def all(queryable \\ nil, opts \\ []) do
        actual_queryable = queryable || Module.concat(__MODULE__, Struct)

        Drops.Relation.all(
          actual_queryable,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_one_function(repo) do
    quote do
      def one(queryable, opts \\ []) do
        Drops.Relation.one(
          queryable,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_one_bang_function(repo) do
    quote do
      def one!(queryable, opts \\ []) do
        Drops.Relation.one!(
          queryable,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_insert_function(repo) do
    quote do
      def insert(struct_or_changeset_or_map, opts \\ []) do
        Drops.Relation.insert(
          struct_or_changeset_or_map,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_insert_bang_function(repo) do
    quote do
      def insert!(struct_or_changeset_or_map, opts \\ []) do
        Drops.Relation.insert!(
          struct_or_changeset_or_map,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_update_function(repo) do
    quote do
      def update(changeset, opts \\ []) do
        Drops.Relation.update(
          changeset,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_update_bang_function(repo) do
    quote do
      def update!(changeset, opts \\ []) do
        Drops.Relation.update!(
          changeset,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_delete_function(repo) do
    quote do
      def delete(struct, opts \\ []) do
        Drops.Relation.delete(
          struct,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_delete_bang_function(repo) do
    quote do
      def delete!(struct, opts \\ []) do
        Drops.Relation.delete!(
          struct,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_count_function(repo) do
    quote do
      def count(queryable \\ nil, opts \\ []) do
        actual_queryable = queryable || Module.concat(__MODULE__, Struct)

        Drops.Relation.count(
          actual_queryable,
          :count,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_first_function(repo) do
    quote do
      def first(queryable \\ nil, opts \\ []) do
        actual_queryable = queryable || Module.concat(__MODULE__, Struct)

        Drops.Relation.first(
          actual_queryable,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  defp generate_delegating_last_function(repo) do
    quote do
      def last(queryable \\ nil, opts \\ []) do
        actual_queryable = queryable || Module.concat(__MODULE__, Struct)

        Drops.Relation.last(
          actual_queryable,
          opts |> Keyword.put(:repo, unquote(repo)) |> Keyword.put(:relation, __MODULE__)
        )
      end
    end
  end

  # Generates index-based finder functions
  defp generate_index_based_finders(repo, drops_schema) do
    alias Drops.Relation.Schema.Index

    # Get all indices from the schema
    indices = drops_schema.indices.indices

    # Generate finders for single-field indices
    single_field_finders =
      indices
      |> Enum.reject(&Index.composite?/1)
      |> Enum.map(&generate_single_field_finder(repo, &1))

    # For now, skip composite indices (can be added later)
    single_field_finders
  end

  defp generate_single_field_finder(_repo, index) do
    alias Drops.Relation.Schema.Index
    [field] = Index.field_names(index)
    get_function_name = String.to_atom("get_by_#{field}")
    find_function_name = String.to_atom("find_by_#{field}")

    quote do
      # get_by_* function that returns a composable relation
      def unquote(get_function_name)(value) do
        __MODULE__.restrict(__MODULE__, [{unquote(field), value}])
      end

      def unquote(get_function_name)(queryable, value) do
        __MODULE__.restrict(queryable, [{unquote(field), value}])
      end

      # New find_by_* function that returns a composable relation
      def unquote(find_function_name)(value) do
        __MODULE__.restrict(__MODULE__, [{unquote(field), value}])
      end

      def unquote(find_function_name)(queryable, value) do
        __MODULE__.restrict(queryable, [{unquote(field), value}])
      end
    end
  end

  defmodule Inference do
    alias Drops.Relation.SQL

    def infer_schema(relation, name, repo) do
      # Use the unified schema inference implementation
      drops_schema = SQL.Inference.infer_from_table(name, repo)

      # Generate Ecto schema fields from the inferred schema
      field_definitions = generate_field_definitions_from_schema(relation, drops_schema)

      # Get optional Ecto associations definitions AST
      association_definitions = Module.get_attribute(relation, :associations, [])

      # Create the Ecto schema AST
      ecto_schema =
        quote location: :keep do
          schema unquote(name) do
            (unquote_splicing(field_definitions))

            unquote(association_definitions)
          end
        end

      {ecto_schema, drops_schema}
    end

    def infer_schema_fields_only(relation, name, repo) do
      # Use the unified schema inference implementation
      drops_schema = SQL.Inference.infer_from_table(name, repo)

      # Generate Ecto schema fields from the inferred schema
      field_definitions = generate_field_definitions_from_schema(relation, drops_schema)

      # Create the Ecto schema AST with fields only (no associations)
      ecto_schema =
        quote location: :keep do
          schema unquote(name) do
            (unquote_splicing(field_definitions))
          end
        end

      {ecto_schema, drops_schema}
    end

    # Generates Ecto field definitions from a Drops.Relation.Schema
    defp generate_field_definitions_from_schema(relation, schema) do
      # Check if we have timestamp fields
      has_inserted_at = Enum.any?(schema.fields, &(&1.name == :inserted_at))
      has_updated_at = Enum.any?(schema.fields, &(&1.name == :updated_at))
      has_timestamps = has_inserted_at and has_updated_at

      custom_fields = Module.get_attribute(relation, :fields, [])

      IO.inspect(custom_fields)

      # Generate regular field definitions (excluding id and timestamp fields)
      field_definitions =
        schema.fields
        |> Enum.reject(fn field ->
          # Skip primary key named 'id' and timestamp fields (handled separately)
          field.name in [:id, :inserted_at, :updated_at]
        end)
        |> Enum.map(fn field ->
          quote do
            field(unquote(field.name), unquote(field.ecto_type))
          end
        end)

      # Add timestamps() macro if we have both timestamp fields
      if has_timestamps do
        field_definitions ++
          [
            quote do
              timestamps()
            end
          ]
      else
        field_definitions
      end
    end
  end

  # Helper function to combine base schema with associations
  defp combine_schema_with_associations(
         base_schema_ast,
         association_definitions,
         table_name
       ) do
    # Extract the field definitions from the base schema
    field_definitions = extract_field_definitions_from_schema_ast(base_schema_ast)

    # Create new schema AST that combines fields with associations
    quote location: :keep do
      schema unquote(table_name) do
        (unquote_splicing(field_definitions))

        unquote(association_definitions)
      end
    end
  end

  # Helper function to extract field definitions from schema AST
  defp extract_field_definitions_from_schema_ast(
         {:schema, _meta,
          [_table_name, [do: {:__block__, _block_meta, field_definitions}]]}
       ) do
    field_definitions
  end

  defp extract_field_definitions_from_schema_ast(
         {:schema, _meta, [_table_name, [do: single_field]]}
       ) do
    [single_field]
  end

  defp extract_field_definitions_from_schema_ast(_) do
    []
  end
end
