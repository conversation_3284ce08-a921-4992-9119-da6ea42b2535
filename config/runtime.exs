import Config

env = Mix.env()

if adapter = System.get_env("ADAPTER") do
  config :drops, ecto_repos: [Module.concat(["Drops", "Repos", adapter])]
end

# Ensure log directory exists
log_dir = Path.join(File.cwd!(), "log")

# Configure logger to write Ecto logs to file
config :logger,
  backends: [
    {LoggerFileBackend, :ecto},
    {LoggerFileBackend, :drops}
  ]

# Configure Ecto file logger
config :logger, :ecto,
  path: Path.join(log_dir, "ecto_#{env}.log"),
  level: :debug,
  format: "$time $metadata[$level] $message\n",
  metadata: [:query_time, :decode_time, :queue_time, :connection_time]

# Configure Drops file logger
config :logger, :drops,
  path: Path.join(log_dir, "drops_#{env}.log"),
  level: :debug,
  format: "$time $metadata[$level] $message\n"

# Configure the original TestRepo (SQLite) for backward compatibility in dev environment
config :drops, Drops.TestRepo,
  adapter: Ecto.Adapters.SQLite3,
  database: ":memory:",
  pool: Ecto.Adapters.SQL.Sandbox,
  loggers: [{Ecto.LogEntry, :log, [:ecto]}]

# Configure the SQLite repository for examples in dev environment
config :drops, Drops.Repos.Sqlite,
  adapter: Ecto.Adapters.SQLite3,
  database: "priv/sqlite_#{env}.db",
  pool_size: 1,
  pool: Ecto.Adapters.SQL.Sandbox,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  priv: "priv/repo/sqlite",
  loggers: [{Ecto.LogEntry, :log, [:ecto]}]

# Configure the PostgreSQL repository for examples in dev environment
config :drops, Drops.Repos.Postgres,
  adapter: Ecto.Adapters.Postgres,
  username: "postgres",
  password: "postgres",
  hostname: "postgres",
  database: "drops_#{env}",
  pool_size: 10,
  pool: Ecto.Adapters.SQL.Sandbox,
  queue_target: 5000,
  queue_interval: 1000,
  priv: "priv/repo/postgres",
  log: :info,
  loggers: [{Ecto.LogEntry, :log, [:ecto]}]

# Configure schema cache for test environment
config :drops, :schema_cache,
  enabled: true,
  max_entries: 100,
  cleanup_interval: :never
