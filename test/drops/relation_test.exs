defmodule Drops.Relations.SchemaSpec do
  use Drops.RelationCase, async: false

  describe "basic schema inference" do
    @tag relations: [:users]

    test "infers basic fields from users table", %{users: users} do
      assert users.ecto_schema(:fields) == [
               :id,
               :name,
               :email,
               :age,
               :inserted_at,
               :updated_at
             ]

      assert users.ecto_schema(:type, :id) == :id
      assert users.ecto_schema(:type, :name) == :string
      assert users.ecto_schema(:type, :email) == :string
      assert users.ecto_schema(:type, :age) == :integer
      assert users.ecto_schema(:type, :inserted_at) == :naive_datetime
      assert users.ecto_schema(:type, :updated_at) == :naive_datetime
    end
  end

  describe "different field types" do
    @tag relations: [:basic_types]
    test "infers various field types correctly", %{basic_types: basic_types} do
      fields = basic_types.ecto_schema(:fields)

      # Should include all non-timestamp fields
      assert :id in fields
      assert :string_field in fields
      assert :integer_field in fields
      assert :float_field in fields
      assert :boolean_field in fields
      assert :binary_field in fields
      assert :bitstring_field in fields

      # Check types
      assert basic_types.ecto_schema(:type, :string_field) == :string
      assert basic_types.ecto_schema(:type, :integer_field) == :integer
      assert basic_types.ecto_schema(:type, :float_field) == :decimal
      # TODO: add custom ecto type called Boolean for Sqlite inference
      assert basic_types.ecto_schema(:type, :boolean_field) == :integer
      assert basic_types.ecto_schema(:type, :binary_field) == :binary
    end
  end

  describe "primary keys" do
    @describetag relations: [:users]

    @tag relations: [:custom_pk]
    test "handles custom primary keys", %{custom_pk: custom_pk} do
      fields = custom_pk.ecto_schema(:fields)

      # Should include the custom primary key and other fields
      assert :uuid in fields
      assert :name in fields

      # The default :id should still be present (Ecto's default behavior)
      assert :id in fields
    end

    @tag relations: [:no_pk]
    test "handles tables without primary keys", %{no_pk: no_pk} do
      fields = no_pk.ecto_schema(:fields)

      # Should include all fields
      # Ecto still adds this
      assert :id in fields
      assert :name in fields
      assert :value in fields
    end
  end

  describe "foreign keys and associations" do
    @tag relations: [:associations, :association_items, :association_parents]
    test "infers foreign key fields", %{associations: associations} do
      fields = associations.ecto_schema(:fields)

      # Should include foreign key fields
      assert :id in fields
      assert :name in fields
      # belongs_to association
      assert :parent_id in fields

      # Check that foreign key has correct type
      assert associations.ecto_schema(:type, :parent_id) == :id
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "infers association item foreign keys", %{association_items: association_items} do
      fields = association_items.ecto_schema(:fields)

      # Should include foreign key fields
      assert :id in fields
      assert :title in fields
      # belongs_to association
      assert :association_id in fields

      # Check that foreign key has correct type
      assert association_items.ecto_schema(:type, :association_id) == :id
    end
  end

  describe "timestamp handling" do
    @tag relations: [:timestamps]
    test "includes timestamp fields in inference by default", %{timestamps: timestamps} do
      fields = timestamps.ecto_schema(:fields)

      # Should include regular fields and timestamps
      assert :id in fields
      assert :name in fields

      # Timestamps should be included in inference by default
      assert :inserted_at in fields
      assert :updated_at in fields
    end
  end

  describe "automatic schema storage" do
    @tag relations: [:users]
    test "automatically stores Drops.Relation.Schema", %{users: users} do
      schema = users.schema()

      # Should be a Drops.Relation.Schema struct
      assert %Drops.Relation.Schema{} = schema
      assert schema.source == "users"

      # Should have primary key information
      assert Drops.Relation.Schema.PrimaryKey.field_names(schema.primary_key) == [:id]

      # Should have field metadata
      assert length(schema.fields) > 0
      assert Enum.any?(schema.fields, &(&1.name == :name))
      assert Enum.any?(schema.fields, &(&1.name == :email))

      # Should have empty foreign keys for simple schema
      assert schema.foreign_keys == []

      # Should have empty virtual fields
      assert schema.virtual_fields == []
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "stores schema with foreign keys and associations", %{associations: associations} do
      schema = associations.schema()

      # Should be a Drops.Relation.Schema struct
      assert %Drops.Relation.Schema{} = schema
      assert schema.source == "associations"

      # Should have primary key information
      assert Drops.Relation.Schema.PrimaryKey.field_names(schema.primary_key) == [:id]

      # Should have field metadata including foreign keys
      assert length(schema.fields) > 0
      parent_id_field = Enum.find(schema.fields, &(&1.name == :parent_id))
      assert parent_id_field != nil
      assert parent_id_field.type == :integer

      # Note: Database introspection cannot extract association metadata
      # since associations are defined in Ecto schema code, not in the database.
      # The generated schema from database introspection will not have associations.
      assert schema.foreign_keys == []
    end

    @tag relations: [:composite_pk]
    test "stores schema with composite primary key", %{composite_pk: composite_pk} do
      schema = composite_pk.schema()

      # Should be a Drops.Relation.Schema struct
      assert %Drops.Relation.Schema{} = schema
      assert schema.source == "composite_pk"

      # Note: Database introspection cannot detect composite primary keys
      # defined with @primary_key false and field-level primary_key: true
      # since SQLite PRAGMA table_info only shows one primary key column.
      # SQLite will show the first primary key column (part1) as the primary key.
      assert Drops.Relation.Schema.PrimaryKey.field_names(schema.primary_key) == [:part1]
      refute Drops.Relation.Schema.composite_primary_key?(schema)
    end
  end

  describe "customizing fields" do
    relation(:users) do
      field(:tags, Ecto.Enum, values: [:red, :green, :blue])
    end

    test "custom fields are respected", %{users: _users} do
      # TODO: ensure that our custom definition is respected
    end
  end
end
