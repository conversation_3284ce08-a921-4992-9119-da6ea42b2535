defmodule Drops.Relations.PreloadTest do
  use Drops.RelationCase, async: false

  describe "preload functionality" do
    @describetag adapter: :sqlite

    relation(:user_groups) do
      associations do
        belongs_to(:user, Test.Relations.UsersSqlite.Struct, define_field: false)
        belongs_to(:group, Test.Relations.GroupsSqlite.Struct, define_field: false)
      end
    end

    relation(:users) do
      associations do
        many_to_many(:groups, Test.Relations.GroupsSqlite.Struct,
          join_through: "user_groups",
          join_keys: [user_id: :id, group_id: :id]
        )
      end
    end

    relation(:groups) do
      associations do
        many_to_many(:users, Test.Relations.UsersSqlite.Struct,
          join_through: "user_groups",
          join_keys: [group_id: :id, user_id: :id]
        )
      end
    end

    test "preload works with many_to_many associations", %{users: users, groups: groups} do
      # Insert test data
      {:ok, user1} = users.insert(%{name: "Alice", email: "<EMAIL>"})
      {:ok, user2} = users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

      {:ok, group1} = groups.insert(%{name: "Admins", description: "Admin group"})
      {:ok, group2} = groups.insert(%{name: "Users", description: "Regular users"})

      # Create associations manually by inserting into join table
      Drops.Repos.Sqlite.insert_all("user_groups", [
        %{
          user_id: user1.id,
          group_id: group1.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        },
        %{
          user_id: user1.id,
          group_id: group2.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        },
        %{
          user_id: user2.id,
          group_id: group2.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
      ])

      # Test preloading groups for users
      users_with_groups = users.preload(:groups)
      results = Enum.to_list(users_with_groups)

      assert length(results) == 2

      # Find Alice and verify her groups
      alice = Enum.find(results, &(&1.name == "Alice"))
      assert alice != nil
      assert Ecto.assoc_loaded?(alice.groups)
      assert length(alice.groups) == 2

      alice_group_names = Enum.map(alice.groups, & &1.name) |> Enum.sort()
      assert alice_group_names == ["Admins", "Users"]

      # Find Bob and verify his groups
      bob = Enum.find(results, &(&1.name == "Bob"))
      assert bob != nil
      assert Ecto.assoc_loaded?(bob.groups)
      assert length(bob.groups) == 1
      assert hd(bob.groups).name == "Users"
    end

    test "preload works in reverse direction for many_to_many", %{
      users: users,
      groups: groups
    } do
      # Insert test data
      {:ok, user1} = users.insert(%{name: "Alice", email: "<EMAIL>"})
      {:ok, user2} = users.insert(%{name: "Bob", email: "<EMAIL>"})

      {:ok, group1} = groups.insert(%{name: "Admins", description: "Admin group"})

      # Create associations
      Drops.Repos.Sqlite.insert_all("user_groups", [
        %{
          user_id: user1.id,
          group_id: group1.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        },
        %{
          user_id: user2.id,
          group_id: group1.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
      ])

      # Test preloading users for groups
      groups_with_users = groups.preload(:users)
      results = Enum.to_list(groups_with_users)

      assert length(results) == 1
      [admin_group] = results

      assert admin_group.name == "Admins"
      assert Ecto.assoc_loaded?(admin_group.users)
      assert length(admin_group.users) == 2

      user_names = Enum.map(admin_group.users, & &1.name) |> Enum.sort()
      assert user_names == ["Alice", "Bob"]
    end

    test "preload with restrictions on many_to_many", %{users: users, groups: groups} do
      # Insert test data
      {:ok, user1} = users.insert(%{name: "Alice", email: "<EMAIL>"})
      {:ok, user2} = users.insert(%{name: "Bob", email: "<EMAIL>"})

      {:ok, group1} = groups.insert(%{name: "Admins", description: "Admin group"})
      {:ok, group2} = groups.insert(%{name: "Users", description: "Regular users"})

      # Create associations
      Drops.Repos.Sqlite.insert_all("user_groups", [
        %{
          user_id: user1.id,
          group_id: group1.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        },
        %{
          user_id: user2.id,
          group_id: group2.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
      ])

      # Test preloading with restriction
      alice_with_groups = users.restrict(name: "Alice") |> users.preload(:groups)
      results = Enum.to_list(alice_with_groups)

      assert length(results) == 1
      [alice] = results

      assert alice.name == "Alice"
      assert Ecto.assoc_loaded?(alice.groups)
      assert length(alice.groups) == 1
      assert hd(alice.groups).name == "Admins"
    end

    test "preload multiple associations", %{users: users, groups: groups} do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Multi User", email: "<EMAIL>"})
      {:ok, group} = groups.insert(%{name: "Test Group", description: "Test"})

      # Create association
      Drops.Repos.Sqlite.insert_all("user_groups", [
        %{
          user_id: user.id,
          group_id: group.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
      ])

      # Test preloading multiple associations (even though we only have groups in this test)
      users_with_preloads = users.preload([:groups])
      results = Enum.to_list(users_with_preloads)

      multi_user = Enum.find(results, &(&1.name == "Multi User"))
      assert multi_user != nil
      assert Ecto.assoc_loaded?(multi_user.groups)
      assert length(multi_user.groups) == 1
    end

    test "chaining preload calls", %{users: users, groups: groups} do
      # Insert test data
      {:ok, user} = users.insert(%{name: "Chain User", email: "<EMAIL>"})
      {:ok, group} = groups.insert(%{name: "Chain Group", description: "Test"})

      # Create association
      Drops.Repos.Sqlite.insert_all("user_groups", [
        %{
          user_id: user.id,
          group_id: group.id,
          inserted_at: DateTime.utc_now(),
          updated_at: DateTime.utc_now()
        }
      ])

      # Test chaining preload calls
      users_with_groups = users.preload(:groups) |> users.preload(:groups)
      results = Enum.to_list(users_with_groups)

      chain_user = Enum.find(results, &(&1.name == "Chain User"))
      assert chain_user != nil
      assert Ecto.assoc_loaded?(chain_user.groups)
      assert length(chain_user.groups) == 1
    end

    test "preload with empty results", %{users: users} do
      # Test preloading when no data exists
      users_with_groups = users.preload(:groups)
      results = Enum.to_list(users_with_groups)

      assert results == []
    end

    test "preload with no associations", %{users: users} do
      # Insert user without any group associations
      {:ok, _user} = users.insert(%{name: "Lonely User", email: "<EMAIL>"})

      users_with_groups = users.preload(:groups)
      results = Enum.to_list(users_with_groups)

      assert length(results) == 1
      [lonely_user] = results

      assert lonely_user.name == "Lonely User"
      assert Ecto.assoc_loaded?(lonely_user.groups)
      assert lonely_user.groups == []
    end
  end
end
